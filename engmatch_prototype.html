<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EngMatch - 工程项目撮合平台原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
        }
        
        .nav-item {
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        /* 页面内容区域 */
        .page {
            display: none;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }
        
        .page.active {
            display: block;
        }
        
        /* 首页样式 */
        .hero-section {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            margin: -2rem -2rem 2rem -2rem;
        }
        
        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        /* 项目列表 */
        .project-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .project-list {
            display: grid;
            gap: 1.5rem;
        }
        
        .project-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }
        
        .project-status {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-open {
            background: #e8f5e8;
            color: #4CAF50;
        }
        
        .status-bidding {
            background: #fff3cd;
            color: #856404;
        }
        
        .project-meta {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .project-actions {
            display: flex;
            gap: 1rem;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            min-height: 120px;
            resize: vertical;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .project-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .project-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        /* 个人中心样式 */
        .profile-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
        }

        .profile-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .avatar {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .profile-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 2rem;
        }

        .tab-btn {
            padding: 1rem 2rem;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            display: flex;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .info-item label {
            font-weight: bold;
            margin-right: 1rem;
            min-width: 80px;
        }

        .project-summary {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .summary-item {
            text-align: center;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex: 1;
        }

        .summary-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .summary-label {
            color: #666;
            margin-top: 0.5rem;
        }

        .certificate-list {
            display: grid;
            gap: 1rem;
        }

        .certificate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .cert-status {
            color: #4CAF50;
            font-weight: bold;
        }

        /* 消息页面样式 */
        .messages-container {
            display: grid;
            grid-template-columns: 350px 1fr;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }

        .message-sidebar {
            background: #f8f9fa;
            border-right: 1px solid #ddd;
        }

        .message-search {
            padding: 1rem;
            border-bottom: 1px solid #ddd;
        }

        .contact-list {
            overflow-y: auto;
            height: calc(100% - 80px);
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }

        .contact-item:hover, .contact-item.active {
            background: white;
        }

        .contact-avatar {
            font-size: 2rem;
            margin-right: 1rem;
        }

        .contact-info {
            flex: 1;
        }

        .contact-name {
            font-weight: bold;
            margin-bottom: 0.3rem;
        }

        .contact-message {
            font-size: 0.9rem;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-time {
            font-size: 0.8rem;
            color: #999;
        }

        .chat-area {
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
            background: white;
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            flex-direction: column;
        }

        .message.sent {
            align-items: flex-end;
        }

        .message.received {
            align-items: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 0.8rem 1rem;
            border-radius: 10px;
            word-wrap: break-word;
        }

        .message.sent .message-content {
            background: #667eea;
            color: white;
        }

        .message.received .message-content {
            background: white;
            border: 1px solid #ddd;
        }

        .message .message-time {
            font-size: 0.8rem;
            color: #999;
            margin-top: 0.3rem;
        }

        .chat-input {
            display: flex;
            padding: 1rem;
            border-top: 1px solid #ddd;
            background: white;
            gap: 1rem;
        }

        .message-input {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }

        /* 抖音风格样式 */
        .tiktok-container {
            position: relative;
            height: calc(100vh - 80px);
            overflow: hidden;
        }

        .video-item {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            opacity: 0;
            transform: translateY(100%);
            transition: all 0.5s ease-in-out;
        }

        .video-item.active {
            opacity: 1;
            transform: translateY(0);
        }

        .video-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-size: cover;
            background-position: center;
            z-index: 1;
        }

        .video-content {
            position: relative;
            z-index: 2;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            padding: 2rem;
            background: linear-gradient(transparent 0%, rgba(0,0,0,0.3) 70%, rgba(0,0,0,0.7) 100%);
        }

        .video-info {
            color: white;
        }

        .user-info {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }

        .user-info .avatar {
            font-size: 2.5rem;
            margin-right: 1rem;
        }

        .username {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 0.3rem;
        }

        .follow-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s;
        }

        .follow-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .video-description h3 {
            font-size: 1.3rem;
            margin-bottom: 0.5rem;
            line-height: 1.4;
        }

        .video-description p {
            margin-bottom: 0.5rem;
            line-height: 1.5;
            opacity: 0.9;
        }

        .hashtags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 1rem;
        }

        .hashtag {
            background: rgba(255,255,255,0.2);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.9rem;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .video-actions {
            position: relative;
            z-index: 2;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1.5rem;
            padding: 2rem 1rem;
            justify-content: flex-end;
            min-width: 80px;
        }

        .action-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            transition: transform 0.3s;
        }

        .action-btn:hover {
            transform: scale(1.1);
        }

        .action-icon {
            font-size: 2rem;
            margin-bottom: 0.3rem;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
        }

        .action-count {
            color: white;
            font-size: 0.8rem;
            font-weight: bold;
            text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        }

        .bid-btn {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            border-radius: 20px;
            padding: 0.8rem;
            color: white;
            box-shadow: 0 4px 15px rgba(255,107,107,0.4);
        }

        .scroll-indicator {
            position: absolute;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
            color: white;
            z-index: 3;
            animation: bounce 2s infinite;
        }

        .scroll-text {
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            opacity: 0.8;
        }

        .scroll-arrow {
            font-size: 1.5rem;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(-50%) translateY(0);
            }
            40% {
                transform: translateX(-50%) translateY(-10px);
            }
            60% {
                transform: translateX(-50%) translateY(-5px);
            }
        }

        /* 小红书风格样式 */
        .xiaohongshu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding: 0 1rem;
        }

        .xiaohongshu-header h2 {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
        }

        .search-bar {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            border-radius: 20px;
            padding: 0.5rem 1rem;
            min-width: 300px;
        }

        .search-input {
            border: none;
            background: none;
            outline: none;
            flex: 1;
            padding: 0.3rem;
            font-size: 0.9rem;
        }

        .search-btn {
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1.1rem;
        }

        .waterfall-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1rem;
            padding: 0 1rem;
        }

        .waterfall-column {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .project-card-xhs {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            cursor: pointer;
            break-inside: avoid;
        }

        .project-card-xhs:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .project-image {
            position: relative;
            overflow: hidden;
        }

        .image-placeholder {
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            border-radius: 12px 12px 0 0;
        }

        .image-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            color: white;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .project-type {
            font-size: 3rem;
            margin-bottom: 0.5rem;
        }

        .project-budget {
            font-size: 1.2rem;
            font-weight: bold;
            background: rgba(255,255,255,0.2);
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .project-status-badge {
            position: absolute;
            top: 0.8rem;
            right: 0.8rem;
            padding: 0.3rem 0.8rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: bold;
            backdrop-filter: blur(10px);
        }

        .status-hot {
            background: rgba(255,107,107,0.9);
            color: white;
        }

        .status-new {
            background: rgba(76,175,80,0.9);
            color: white;
        }

        .status-urgent {
            background: rgba(255,193,7,0.9);
            color: #333;
        }

        .status-premium {
            background: rgba(156,39,176,0.9);
            color: white;
        }

        .project-info-xhs {
            padding: 1rem;
        }

        .project-title-xhs {
            font-size: 1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.8rem;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .project-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-bottom: 1rem;
        }

        .tag {
            background: #f0f0f0;
            color: #666;
            padding: 0.2rem 0.6rem;
            border-radius: 10px;
            font-size: 0.8rem;
        }

        .project-stats {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-top: 0.5rem;
            border-top: 1px solid #f0f0f0;
        }

        .stat-item {
            display: flex;
            align-items: center;
            gap: 0.3rem;
            font-size: 0.9rem;
            color: #666;
        }

        .stat-icon {
            font-size: 1rem;
        }

        .stat-number {
            font-weight: 500;
        }

        .publisher-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .publisher-avatar {
            font-size: 1.5rem;
        }

        .publisher-name {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        /* 小红书详情模态框样式 */
        .xhs-detail {
            max-width: 800px;
            width: 95%;
            max-height: 90vh;
            padding: 0;
        }

        .project-detail-content {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .detail-images {
            margin-bottom: 1.5rem;
        }

        .main-image .image-placeholder {
            border-radius: 8px;
        }

        .detail-info {
            padding: 0 2rem 2rem 2rem;
            overflow-y: auto;
        }

        .detail-header {
            margin-bottom: 1.5rem;
        }

        .detail-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
            line-height: 1.4;
        }

        .detail-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
            padding: 1.5rem;
            background: #f8f9fa;
            border-radius: 12px;
        }

        .detail-stats .stat-item {
            display: flex;
            flex-direction: column;
            text-align: center;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.3rem;
        }

        .stat-value {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
        }

        .detail-description {
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .detail-description h4 {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin: 1.5rem 0 0.8rem 0;
        }

        .detail-description p {
            margin-bottom: 1rem;
            color: #555;
        }

        .detail-description ul {
            margin-bottom: 1rem;
            padding-left: 1.5rem;
        }

        .detail-description li {
            margin-bottom: 0.5rem;
            color: #555;
        }

        .detail-publisher {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 12px;
            margin-bottom: 1.5rem;
        }

        .detail-publisher .publisher-avatar {
            font-size: 2.5rem;
        }

        .publisher-details {
            flex: 1;
        }

        .detail-publisher .publisher-name {
            font-size: 1.1rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 0.3rem;
        }

        .publisher-info {
            font-size: 0.9rem;
            color: #666;
        }

        .detail-actions {
            display: flex;
            gap: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
            flex-wrap: wrap;
        }

        .action-btn-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.2rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .action-btn-detail:hover {
            background: #f5f5f5;
            transform: translateY(-1px);
        }

        .like-btn:hover {
            border-color: #ff6b6b;
            color: #ff6b6b;
        }

        .comment-btn:hover {
            border-color: #4ecdc4;
            color: #4ecdc4;
        }

        .share-btn:hover {
            border-color: #45b7d1;
            color: #45b7d1;
        }

        .collect-btn:hover {
            border-color: #f39c12;
            color: #f39c12;
        }

        .bid-btn-detail {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.8rem 1.5rem;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
            font-size: 0.9rem;
            font-weight: bold;
            margin-left: auto;
        }

        .bid-btn-detail:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102,126,234,0.4);
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="logo">EngMatch</div>
            <div class="nav-menu">
                <div class="nav-item active" onclick="showPage('home')">首页</div>
                <div class="nav-item" onclick="showPage('projects')">项目大厅</div>
                <div class="nav-item" onclick="showPage('publish')">发布项目</div>
                <div class="nav-item" onclick="showPage('profile')">个人中心</div>
                <div class="nav-item" onclick="showPage('messages')">消息</div>
            </div>
            <div class="user-info">
                <span>欢迎，张工程师</span>
                <button class="btn btn-secondary" onclick="showLoginModal()">登录</button>
            </div>
        </nav>

        <!-- 首页 - 抖音风格 -->
        <div id="home" class="page active">
            <div class="tiktok-container">
                <div class="video-item active" data-index="0">
                    <div class="video-background" style="background: linear-gradient(45deg, #667eea 0%, #764ba2 100%), url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 1200 800%22><rect fill=%22%23667eea%22 width=%221200%22 height=%22800%22/><circle fill=%22%23764ba2%22 cx=%22300%22 cy=%22200%22 r=%22100%22 opacity=%220.3%22/><circle fill=%22%23764ba2%22 cx=%22900%22 cy=%22600%22 r=%22150%22 opacity=%220.2%22/></svg>'); background-blend-mode: overlay;"></div>
                    <div class="video-content">
                        <div class="video-info">
                            <div class="user-info">
                                <div class="avatar">🏢</div>
                                <div class="user-details">
                                    <div class="username">@北京建设集团</div>
                                    <div class="follow-btn">+ 关注</div>
                                </div>
                            </div>
                            <div class="video-description">
                                <h3>🏗️ 北京CBD商业综合体项目</h3>
                                <p>5000万预算 | 24个月工期 | 8万㎡建筑面积</p>
                                <p>寻找有经验的建筑施工团队，要求一级资质 💪</p>
                                <div class="hashtags">
                                    <span class="hashtag">#建筑工程</span>
                                    <span class="hashtag">#商业综合体</span>
                                    <span class="hashtag">#北京项目</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="video-actions">
                        <div class="action-btn">
                            <div class="action-icon">❤️</div>
                            <span class="action-count">1.2k</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">💬</div>
                            <span class="action-count">89</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">📤</div>
                            <span class="action-count">分享</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">⭐</div>
                            <span class="action-count">收藏</span>
                        </div>
                        <div class="action-btn bid-btn" onclick="showBidModal('project1')">
                            <div class="action-icon">🎯</div>
                            <span class="action-count">投标</span>
                        </div>
                    </div>
                </div>

                <div class="video-item" data-index="1">
                    <div class="video-background" style="background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%), url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 1200 800%22><rect fill=%22%23f093fb%22 width=%221200%22 height=%22800%22/><path fill=%22%23f5576c%22 d=%22M0,400 Q300,200 600,400 T1200,400 L1200,800 L0,800 Z%22 opacity=%220.3%22/></svg>'); background-blend-mode: overlay;"></div>
                    <div class="video-content">
                        <div class="video-info">
                            <div class="user-info">
                                <div class="avatar">🌉</div>
                                <div class="user-details">
                                    <div class="username">@浙江交通建设</div>
                                    <div class="follow-btn">+ 关注</div>
                                </div>
                            </div>
                            <div class="video-description">
                                <h3>🌉 杭州湾跨海大桥维护工程</h3>
                                <p>1200万预算 | 6个月工期 | 桥梁专业</p>
                                <p>需要有桥梁维护经验的专业团队 🔧</p>
                                <div class="hashtags">
                                    <span class="hashtag">#桥梁工程</span>
                                    <span class="hashtag">#维护保养</span>
                                    <span class="hashtag">#浙江项目</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="video-actions">
                        <div class="action-btn">
                            <div class="action-icon">❤️</div>
                            <span class="action-count">856</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">💬</div>
                            <span class="action-count">45</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">📤</div>
                            <span class="action-count">分享</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">⭐</div>
                            <span class="action-count">收藏</span>
                        </div>
                        <div class="action-btn bid-btn" onclick="showBidModal('project2')">
                            <div class="action-icon">�</div>
                            <span class="action-count">投标</span>
                        </div>
                    </div>
                </div>

                <div class="video-item" data-index="2">
                    <div class="video-background" style="background: linear-gradient(45deg, #4facfe 0%, #00f2fe 100%), url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 1200 800%22><rect fill=%22%234facfe%22 width=%221200%22 height=%22800%22/><circle fill=%22%2300f2fe%22 cx=%22600%22 cy=%22400%22 r=%22200%22 opacity=%220.3%22/></svg>'); background-blend-mode: overlay;"></div>
                    <div class="video-content">
                        <div class="video-info">
                            <div class="user-info">
                                <div class="avatar">💧</div>
                                <div class="user-details">
                                    <div class="username">@成都水务局</div>
                                    <div class="follow-btn">+ 关注</div>
                                </div>
                            </div>
                            <div class="video-description">
                                <h3>💧 污水处理厂升级改造</h3>
                                <p>3000万预算 | 18个月工期 | 环保工程</p>
                                <p>提升处理能力和出水标准，需要环保资质 🌱</p>
                                <div class="hashtags">
                                    <span class="hashtag">#水利工程</span>
                                    <span class="hashtag">#环保项目</span>
                                    <span class="hashtag">#成都项目</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="video-actions">
                        <div class="action-btn">
                            <div class="action-icon">❤️</div>
                            <span class="action-count">2.1k</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">�</div>
                            <span class="action-count">156</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">📤</div>
                            <span class="action-count">分享</span>
                        </div>
                        <div class="action-btn">
                            <div class="action-icon">⭐</div>
                            <span class="action-count">收藏</span>
                        </div>
                        <div class="action-btn bid-btn" onclick="showBidModal('project3')">
                            <div class="action-icon">🎯</div>
                            <span class="action-count">投标</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 滑动指示器 -->
            <div class="scroll-indicator">
                <div class="scroll-text">滑动查看更多项目</div>
                <div class="scroll-arrow">↓</div>
            </div>
        </div>

        <!-- 项目大厅 - 小红书风格 -->
        <div id="projects" class="page">
            <div class="xiaohongshu-header">
                <h2>发现好项目</h2>
                <div class="search-bar">
                    <input type="text" placeholder="搜索项目..." class="search-input">
                    <button class="search-btn">🔍</button>
                </div>
            </div>

            <div class="project-filters">
                <button class="filter-btn active" onclick="filterProjects('all')">推荐</button>
                <button class="filter-btn" onclick="filterProjects('building')">建筑</button>
                <button class="filter-btn" onclick="filterProjects('road')">桥梁</button>
                <button class="filter-btn" onclick="filterProjects('water')">水利</button>
                <button class="filter-btn" onclick="filterProjects('electric')">电力</button>
                <button class="filter-btn" onclick="filterProjects('hot')">热门</button>
            </div>

            <div class="waterfall-container">
                <div class="waterfall-column">
                    <div class="project-card-xhs" data-category="building" onclick="showProjectDetailXHS('project1')">
                        <div class="project-image">
                            <div class="image-placeholder" style="background: linear-gradient(45deg, #667eea, #764ba2); height: 200px;">
                                <div class="image-content">
                                    <div class="project-type">🏗️</div>
                                    <div class="project-budget">5000万</div>
                                </div>
                            </div>
                            <div class="project-status-badge status-hot">🔥 热门</div>
                        </div>
                        <div class="project-info-xhs">
                            <h3 class="project-title-xhs">北京CBD商业综合体建设项目</h3>
                            <div class="project-tags">
                                <span class="tag">#建筑工程</span>
                                <span class="tag">#商业综合体</span>
                                <span class="tag">#一级资质</span>
                            </div>
                            <div class="project-stats">
                                <div class="stat-item">
                                    <span class="stat-icon">❤️</span>
                                    <span class="stat-number">1.2k</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">💬</span>
                                    <span class="stat-number">89</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-number">收藏</span>
                                </div>
                            </div>
                            <div class="publisher-info">
                                <div class="publisher-avatar">🏢</div>
                                <span class="publisher-name">北京建设集团</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-card-xhs" data-category="water" onclick="showProjectDetailXHS('project3')">
                        <div class="project-image">
                            <div class="image-placeholder" style="background: linear-gradient(45deg, #4facfe, #00f2fe); height: 250px;">
                                <div class="image-content">
                                    <div class="project-type">💧</div>
                                    <div class="project-budget">3000万</div>
                                </div>
                            </div>
                            <div class="project-status-badge status-new">🆕 最新</div>
                        </div>
                        <div class="project-info-xhs">
                            <h3 class="project-title-xhs">成都污水处理厂升级改造</h3>
                            <div class="project-tags">
                                <span class="tag">#水利工程</span>
                                <span class="tag">#环保项目</span>
                                <span class="tag">#政府项目</span>
                            </div>
                            <div class="project-stats">
                                <div class="stat-item">
                                    <span class="stat-icon">❤️</span>
                                    <span class="stat-number">2.1k</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">💬</span>
                                    <span class="stat-number">156</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-number">收藏</span>
                                </div>
                            </div>
                            <div class="publisher-info">
                                <div class="publisher-avatar">💧</div>
                                <span class="publisher-name">成都水务局</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="waterfall-column">
                    <div class="project-card-xhs" data-category="road" onclick="showProjectDetailXHS('project2')">
                        <div class="project-image">
                            <div class="image-placeholder" style="background: linear-gradient(45deg, #f093fb, #f5576c); height: 180px;">
                                <div class="image-content">
                                    <div class="project-type">🌉</div>
                                    <div class="project-budget">1200万</div>
                                </div>
                            </div>
                            <div class="project-status-badge status-urgent">⚡ 紧急</div>
                        </div>
                        <div class="project-info-xhs">
                            <h3 class="project-title-xhs">杭州湾跨海大桥维护工程</h3>
                            <div class="project-tags">
                                <span class="tag">#桥梁工程</span>
                                <span class="tag">#维护保养</span>
                                <span class="tag">#专业承包</span>
                            </div>
                            <div class="project-stats">
                                <div class="stat-item">
                                    <span class="stat-icon">❤️</span>
                                    <span class="stat-number">856</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">💬</span>
                                    <span class="stat-number">45</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-number">收藏</span>
                                </div>
                            </div>
                            <div class="publisher-info">
                                <div class="publisher-avatar">🌉</div>
                                <span class="publisher-name">浙江交通建设</span>
                            </div>
                        </div>
                    </div>

                    <div class="project-card-xhs" data-category="building" onclick="showProjectDetailXHS('project4')">
                        <div class="project-image">
                            <div class="image-placeholder" style="background: linear-gradient(45deg, #a8edea, #fed6e3); height: 220px;">
                                <div class="image-content">
                                    <div class="project-type">🏠</div>
                                    <div class="project-budget">8000万</div>
                                </div>
                            </div>
                            <div class="project-status-badge status-premium">💎 优质</div>
                        </div>
                        <div class="project-info-xhs">
                            <h3 class="project-title-xhs">上海高端住宅小区建设</h3>
                            <div class="project-tags">
                                <span class="tag">#住宅建设</span>
                                <span class="tag">#高端项目</span>
                                <span class="tag">#上海</span>
                            </div>
                            <div class="project-stats">
                                <div class="stat-item">
                                    <span class="stat-icon">❤️</span>
                                    <span class="stat-number">3.5k</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">💬</span>
                                    <span class="stat-number">234</span>
                                </div>
                                <div class="stat-item">
                                    <span class="stat-icon">⭐</span>
                                    <span class="stat-number">收藏</span>
                                </div>
                            </div>
                            <div class="publisher-info">
                                <div class="publisher-avatar">🏙️</div>
                                <span class="publisher-name">上海地产集团</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布项目页面 -->
        <div id="publish" class="page">
            <h2>发布项目</h2>
            <form class="project-form" onsubmit="submitProject(event)">
                <div class="form-group">
                    <label class="form-label">项目名称</label>
                    <input type="text" class="form-input" placeholder="请输入项目名称" required>
                </div>

                <div class="form-group">
                    <label class="form-label">项目类型</label>
                    <select class="form-input" required>
                        <option value="">请选择项目类型</option>
                        <option value="building">建筑工程</option>
                        <option value="road">道路桥梁</option>
                        <option value="water">水利工程</option>
                        <option value="electric">电力工程</option>
                        <option value="other">其他工程</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">项目地址</label>
                    <input type="text" class="form-input" placeholder="请输入项目所在地址" required>
                </div>

                <div class="form-group">
                    <label class="form-label">项目预算（万元）</label>
                    <input type="number" class="form-input" placeholder="请输入项目预算" required>
                </div>

                <div class="form-group">
                    <label class="form-label">计划工期（月）</label>
                    <input type="number" class="form-input" placeholder="请输入计划工期" required>
                </div>

                <div class="form-group">
                    <label class="form-label">资质要求</label>
                    <select class="form-input" required>
                        <option value="">请选择资质要求</option>
                        <option value="level1">一级资质</option>
                        <option value="level2">二级资质</option>
                        <option value="level3">三级资质</option>
                        <option value="special">专业承包资质</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">项目描述</label>
                    <textarea class="form-textarea" placeholder="请详细描述项目需求、技术要求、注意事项等" required></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">投标截止时间</label>
                    <input type="datetime-local" class="form-input" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">发布项目</button>
                </div>
            </form>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile" class="page">
            <h2>个人中心</h2>
            <div class="profile-container">
                <div class="profile-sidebar">
                    <div class="profile-card">
                        <div class="avatar">👨‍💼</div>
                        <h3>张工程师</h3>
                        <p>高级建筑工程师</p>
                        <div class="profile-stats">
                            <div class="stat-item">
                                <span class="stat-number">15</span>
                                <span class="stat-label">完成项目</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">4.8</span>
                                <span class="stat-label">信用评分</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">98%</span>
                                <span class="stat-label">履约率</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="profile-content">
                    <div class="profile-tabs">
                        <button class="tab-btn active" onclick="showProfileTab('info')">基本信息</button>
                        <button class="tab-btn" onclick="showProfileTab('projects')">我的项目</button>
                        <button class="tab-btn" onclick="showProfileTab('bids')">投标记录</button>
                        <button class="tab-btn" onclick="showProfileTab('certificates')">资质证书</button>
                    </div>

                    <div id="profile-info" class="tab-content active">
                        <h3>基本信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>姓名：</label>
                                <span>张工程师</span>
                            </div>
                            <div class="info-item">
                                <label>手机：</label>
                                <span>138****8888</span>
                            </div>
                            <div class="info-item">
                                <label>邮箱：</label>
                                <span><EMAIL></span>
                            </div>
                            <div class="info-item">
                                <label>公司：</label>
                                <span>北京建设工程有限公司</span>
                            </div>
                            <div class="info-item">
                                <label>职位：</label>
                                <span>高级建筑工程师</span>
                            </div>
                            <div class="info-item">
                                <label>从业年限：</label>
                                <span>12年</span>
                            </div>
                        </div>
                    </div>

                    <div id="profile-projects" class="tab-content">
                        <h3>我的项目</h3>
                        <div class="project-summary">
                            <div class="summary-item">
                                <span class="summary-number">3</span>
                                <span class="summary-label">进行中</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-number">15</span>
                                <span class="summary-label">已完成</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-number">2</span>
                                <span class="summary-label">待开始</span>
                            </div>
                        </div>
                    </div>

                    <div id="profile-bids" class="tab-content">
                        <h3>投标记录</h3>
                        <p>最近投标的项目列表...</p>
                    </div>

                    <div id="profile-certificates" class="tab-content">
                        <h3>资质证书</h3>
                        <div class="certificate-list">
                            <div class="certificate-item">
                                <span>✅ 一级建造师证书</span>
                                <span class="cert-status">已认证</span>
                            </div>
                            <div class="certificate-item">
                                <span>✅ 建筑工程施工总承包一级资质</span>
                                <span class="cert-status">已认证</span>
                            </div>
                            <div class="certificate-item">
                                <span>✅ 安全生产许可证</span>
                                <span class="cert-status">已认证</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息页面 -->
        <div id="messages" class="page">
            <h2>消息中心</h2>
            <div class="messages-container">
                <div class="message-sidebar">
                    <div class="message-search">
                        <input type="text" placeholder="搜索联系人..." class="form-input">
                    </div>
                    <div class="contact-list">
                        <div class="contact-item active" onclick="showChat('contact1')">
                            <div class="contact-avatar">🏢</div>
                            <div class="contact-info">
                                <div class="contact-name">上海建设集团</div>
                                <div class="contact-message">关于CBD项目的投标事宜...</div>
                            </div>
                            <div class="message-time">10:30</div>
                        </div>
                        <div class="contact-item" onclick="showChat('contact2')">
                            <div class="contact-avatar">👨‍💼</div>
                            <div class="contact-info">
                                <div class="contact-name">李项目经理</div>
                                <div class="contact-message">项目进度汇报</div>
                            </div>
                            <div class="message-time">昨天</div>
                        </div>
                        <div class="contact-item" onclick="showChat('contact3')">
                            <div class="contact-avatar">🏗️</div>
                            <div class="contact-info">
                                <div class="contact-name">系统通知</div>
                                <div class="contact-message">您有新的投标邀请</div>
                            </div>
                            <div class="message-time">2天前</div>
                        </div>
                    </div>
                </div>

                <div class="chat-area">
                    <div class="chat-header">
                        <h3>上海建设集团</h3>
                        <div class="chat-actions">
                            <button class="btn btn-secondary">📞 语音通话</button>
                            <button class="btn btn-secondary">📹 视频通话</button>
                        </div>
                    </div>

                    <div class="chat-messages">
                        <div class="message received">
                            <div class="message-content">
                                您好，我们对您发布的CBD商业综合体项目很感兴趣，希望能详细了解一下项目需求。
                            </div>
                            <div class="message-time">10:25</div>
                        </div>
                        <div class="message sent">
                            <div class="message-content">
                                您好！感谢您的关注。项目详细需求已经在项目描述中说明，如有具体问题可以随时沟通。
                            </div>
                            <div class="message-time">10:28</div>
                        </div>
                        <div class="message received">
                            <div class="message-content">
                                好的，我们会仔细研究项目需求，预计明天提交投标方案。
                            </div>
                            <div class="message-time">10:30</div>
                        </div>
                    </div>

                    <div class="chat-input">
                        <input type="text" placeholder="输入消息..." class="message-input">
                        <button class="btn btn-primary">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录模态框 -->
        <div id="loginModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>用户登录</h3>
                    <button class="close-btn" onclick="closeModal('loginModal')">&times;</button>
                </div>
                <form onsubmit="login(event)">
                    <div class="form-group">
                        <label class="form-label">手机号/邮箱</label>
                        <input type="text" class="form-input" placeholder="请输入手机号或邮箱" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-input" placeholder="请输入密码" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">登录</button>
                    </div>
                    <div class="form-group" style="text-align: center;">
                        <a href="#" onclick="showRegisterModal()">还没有账号？立即注册</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 投标模态框 -->
        <div id="bidModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>项目投标</h3>
                    <button class="close-btn" onclick="closeModal('bidModal')">&times;</button>
                </div>
                <form onsubmit="submitBid(event)">
                    <div class="form-group">
                        <label class="form-label">投标价格（万元）</label>
                        <input type="number" class="form-input" placeholder="请输入投标价格" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">工期承诺（月）</label>
                        <input type="number" class="form-input" placeholder="请输入工期承诺" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">技术方案</label>
                        <textarea class="form-textarea" placeholder="请简述您的技术方案和优势" required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上传资质文件</label>
                        <input type="file" class="form-input" multiple accept=".pdf,.jpg,.png">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">提交投标</button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 小红书风格项目详情模态框 -->
        <div id="projectDetailModal" class="modal">
            <div class="modal-content xhs-detail">
                <div class="modal-header">
                    <h3>项目详情</h3>
                    <button class="close-btn" onclick="closeModal('projectDetailModal')">&times;</button>
                </div>
                <div class="project-detail-content">
                    <div class="detail-images">
                        <div class="main-image">
                            <div class="image-placeholder" style="background: linear-gradient(45deg, #667eea, #764ba2); height: 300px;">
                                <div class="image-content">
                                    <div class="project-type">🏗️</div>
                                    <div class="project-budget">5000万</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-info">
                        <div class="detail-header">
                            <h2 class="detail-title">北京CBD商业综合体建设项目</h2>
                            <div class="detail-tags">
                                <span class="tag">#建筑工程</span>
                                <span class="tag">#商业综合体</span>
                                <span class="tag">#一级资质</span>
                                <span class="tag">#北京项目</span>
                            </div>
                        </div>

                        <div class="detail-stats">
                            <div class="stat-item">
                                <span class="stat-label">项目预算</span>
                                <span class="stat-value">5000万元</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">计划工期</span>
                                <span class="stat-value">24个月</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">建筑面积</span>
                                <span class="stat-value">8万㎡</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-label">项目地址</span>
                                <span class="stat-value">北京市朝阳区</span>
                            </div>
                        </div>

                        <div class="detail-description">
                            <h4>项目描述</h4>
                            <p>建设面积约8万平方米的商业综合体，包含购物中心、办公楼、酒店等业态。项目位于北京CBD核心区域，地理位置优越，交通便利。</p>
                            <p>要求具备一级建筑资质，有类似大型商业项目经验。施工方需要有完善的质量管理体系和安全管理制度。</p>

                            <h4>技术要求</h4>
                            <ul>
                                <li>建筑结构：钢筋混凝土框架结构</li>
                                <li>抗震等级：8度设防</li>
                                <li>绿色建筑：达到绿色建筑二星标准</li>
                                <li>智能化系统：包含楼宇自控、安防监控等</li>
                            </ul>

                            <h4>资质要求</h4>
                            <ul>
                                <li>建筑工程施工总承包一级资质</li>
                                <li>安全生产许可证</li>
                                <li>ISO9001质量管理体系认证</li>
                                <li>近三年完成类似项目不少于2个</li>
                            </ul>
                        </div>

                        <div class="detail-publisher">
                            <div class="publisher-avatar">🏢</div>
                            <div class="publisher-details">
                                <div class="publisher-name">北京建设集团</div>
                                <div class="publisher-info">发布于 2天前 · 已有12家投标</div>
                            </div>
                            <button class="follow-btn">+ 关注</button>
                        </div>

                        <div class="detail-actions">
                            <button class="action-btn-detail like-btn">
                                <span class="action-icon">❤️</span>
                                <span>1.2k</span>
                            </button>
                            <button class="action-btn-detail comment-btn">
                                <span class="action-icon">💬</span>
                                <span>89</span>
                            </button>
                            <button class="action-btn-detail share-btn">
                                <span class="action-icon">📤</span>
                                <span>分享</span>
                            </button>
                            <button class="action-btn-detail collect-btn">
                                <span class="action-icon">⭐</span>
                                <span>收藏</span>
                            </button>
                            <button class="bid-btn-detail" onclick="showBidModal('project1')">
                                <span class="action-icon">🎯</span>
                                <span>立即投标</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 抖音风格滑动功能
        let currentVideoIndex = 0;
        const videoItems = document.querySelectorAll('.video-item');

        function showVideo(index) {
            videoItems.forEach((item, i) => {
                item.classList.remove('active');
                if (i === index) {
                    item.classList.add('active');
                }
            });
        }

        function nextVideo() {
            currentVideoIndex = (currentVideoIndex + 1) % videoItems.length;
            showVideo(currentVideoIndex);
        }

        function prevVideo() {
            currentVideoIndex = (currentVideoIndex - 1 + videoItems.length) % videoItems.length;
            showVideo(currentVideoIndex);
        }

        // 鼠标滚轮事件
        document.addEventListener('wheel', function(e) {
            if (document.getElementById('home').classList.contains('active')) {
                e.preventDefault();
                if (e.deltaY > 0) {
                    nextVideo();
                } else {
                    prevVideo();
                }
            }
        });

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('home').classList.contains('active')) {
                if (e.key === 'ArrowDown' || e.key === ' ') {
                    e.preventDefault();
                    nextVideo();
                } else if (e.key === 'ArrowUp') {
                    e.preventDefault();
                    prevVideo();
                }
            }
        });

        // 触摸事件支持
        let touchStartY = 0;
        let touchEndY = 0;

        document.addEventListener('touchstart', function(e) {
            touchStartY = e.changedTouches[0].screenY;
        });

        document.addEventListener('touchend', function(e) {
            if (document.getElementById('home').classList.contains('active')) {
                touchEndY = e.changedTouches[0].screenY;
                handleSwipe();
            }
        });

        function handleSwipe() {
            const swipeThreshold = 50;
            const diff = touchStartY - touchEndY;

            if (Math.abs(diff) > swipeThreshold) {
                if (diff > 0) {
                    nextVideo();
                } else {
                    prevVideo();
                }
            }
        }

        // 项目筛选功能
        function filterProjects(category) {
            const projects = document.querySelectorAll('.project-card');
            const filterBtns = document.querySelectorAll('.filter-btn');

            // 更新按钮状态
            filterBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 筛选项目
            projects.forEach(project => {
                if (category === 'all') {
                    project.style.display = 'block';
                } else if (category === 'open') {
                    const status = project.querySelector('.project-status');
                    project.style.display = status.textContent.includes('招标中') ? 'block' : 'none';
                } else {
                    const projectCategory = project.getAttribute('data-category');
                    project.style.display = projectCategory === category ? 'block' : 'none';
                }
            });
        }

        // 个人中心标签切换
        function showProfileTab(tabId) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 显示目标标签内容
            document.getElementById('profile-' + tabId).classList.add('active');

            // 更新标签按钮状态
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 显示聊天
        function showChat(contactId) {
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => contact.classList.remove('active'));
            event.target.classList.add('active');

            // 这里可以加载对应联系人的聊天记录
            console.log('切换到联系人:', contactId);
        }

        // 模态框功能
        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'block';
        }

        function showBidModal(projectId) {
            document.getElementById('bidModal').style.display = 'block';
            console.log('投标项目:', projectId);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 表单提交功能
        function submitProject(event) {
            event.preventDefault();
            alert('项目发布成功！我们将在24小时内审核您的项目。');
            showPage('projects');
        }

        function submitBid(event) {
            event.preventDefault();
            alert('投标提交成功！请等待项目方审核。');
            closeModal('bidModal');
        }

        function login(event) {
            event.preventDefault();
            alert('登录成功！');
            closeModal('loginModal');
            // 更新用户信息显示
            document.querySelector('.user-info span').textContent = '欢迎，张工程师';
        }

        function showProjectDetail(projectId) {
            alert('查看项目详情: ' + projectId);
            // 这里可以跳转到项目详情页面
        }

        // 小红书风格详情页功能
        function showProjectDetailXHS(projectId) {
            // 根据项目ID加载不同的项目详情
            const projectData = {
                'project1': {
                    title: '北京CBD商业综合体建设项目',
                    budget: '5000万',
                    duration: '24个月',
                    area: '8万㎡',
                    location: '北京市朝阳区',
                    type: '🏗️',
                    gradient: 'linear-gradient(45deg, #667eea, #764ba2)',
                    tags: ['#建筑工程', '#商业综合体', '#一级资质', '#北京项目'],
                    publisher: '北京建设集团',
                    publisherAvatar: '🏢',
                    likes: '1.2k',
                    comments: '89'
                },
                'project2': {
                    title: '杭州湾跨海大桥维护工程',
                    budget: '1200万',
                    duration: '6个月',
                    area: '跨海大桥',
                    location: '浙江省嘉兴市',
                    type: '🌉',
                    gradient: 'linear-gradient(45deg, #f093fb, #f5576c)',
                    tags: ['#桥梁工程', '#维护保养', '#专业承包', '#浙江项目'],
                    publisher: '浙江交通建设',
                    publisherAvatar: '🌉',
                    likes: '856',
                    comments: '45'
                },
                'project3': {
                    title: '成都污水处理厂升级改造',
                    budget: '3000万',
                    duration: '18个月',
                    area: '污水处理厂',
                    location: '四川省成都市',
                    type: '💧',
                    gradient: 'linear-gradient(45deg, #4facfe, #00f2fe)',
                    tags: ['#水利工程', '#环保项目', '#政府项目', '#成都项目'],
                    publisher: '成都水务局',
                    publisherAvatar: '💧',
                    likes: '2.1k',
                    comments: '156'
                },
                'project4': {
                    title: '上海高端住宅小区建设',
                    budget: '8000万',
                    duration: '30个月',
                    area: '12万㎡',
                    location: '上海市浦东新区',
                    type: '🏠',
                    gradient: 'linear-gradient(45deg, #a8edea, #fed6e3)',
                    tags: ['#住宅建设', '#高端项目', '#上海', '#精装修'],
                    publisher: '上海地产集团',
                    publisherAvatar: '🏙️',
                    likes: '3.5k',
                    comments: '234'
                }
            };

            const project = projectData[projectId];
            if (!project) return;

            // 更新模态框内容
            const modal = document.getElementById('projectDetailModal');
            const titleElement = modal.querySelector('.detail-title');
            const imageElement = modal.querySelector('.main-image .image-placeholder');
            const typeElement = modal.querySelector('.project-type');
            const budgetElement = modal.querySelector('.project-budget');
            const tagsContainer = modal.querySelector('.detail-tags');
            const statsContainer = modal.querySelector('.detail-stats');
            const publisherName = modal.querySelector('.detail-publisher .publisher-name');
            const publisherAvatar = modal.querySelector('.detail-publisher .publisher-avatar');
            const likesElement = modal.querySelector('.like-btn span:last-child');
            const commentsElement = modal.querySelector('.comment-btn span:last-child');

            // 更新内容
            titleElement.textContent = project.title;
            imageElement.style.background = project.gradient;
            typeElement.textContent = project.type;
            budgetElement.textContent = project.budget;
            publisherName.textContent = project.publisher;
            publisherAvatar.textContent = project.publisherAvatar;
            likesElement.textContent = project.likes;
            commentsElement.textContent = project.comments;

            // 更新标签
            tagsContainer.innerHTML = project.tags.map(tag => `<span class="tag">${tag}</span>`).join('');

            // 更新统计信息
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">项目预算</span>
                    <span class="stat-value">${project.budget}元</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">计划工期</span>
                    <span class="stat-value">${project.duration}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">项目规模</span>
                    <span class="stat-value">${project.area}</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">项目地址</span>
                    <span class="stat-value">${project.location}</span>
                </div>
            `;

            // 显示模态框
            modal.style.display = 'block';
        }

        function showRegisterModal() {
            closeModal('loginModal');
            alert('注册功能开发中...');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 消息发送功能
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.querySelector('.message-input');
            const sendBtn = document.querySelector('.chat-input .btn-primary');

            function sendMessage() {
                const message = messageInput.value.trim();
                if (message) {
                    const chatMessages = document.querySelector('.chat-messages');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message sent';
                    messageDiv.innerHTML = `
                        <div class="message-content">${message}</div>
                        <div class="message-time">${new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}</div>
                    `;
                    chatMessages.appendChild(messageDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    messageInput.value = '';
                }
            }

            if (sendBtn) {
                sendBtn.addEventListener('click', sendMessage);
            }
            if (messageInput) {
                messageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });

        // 模拟实时数据更新
        setInterval(function() {
            // 模拟新消息通知
            const messageTime = document.querySelector('.contact-item.active .message-time');
            if (messageTime && Math.random() > 0.95) {
                messageTime.textContent = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            }
        }, 5000);

        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.feature-card, .project-card, .project-card-xhs');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });

            // 抖音风格的点赞动画
            const actionBtns = document.querySelectorAll('.action-btn');
            actionBtns.forEach(btn => {
                btn.addEventListener('click', function(e) {
                    if (this.querySelector('.action-icon').textContent === '❤️') {
                        // 创建点赞动画
                        const heart = document.createElement('div');
                        heart.innerHTML = '❤️';
                        heart.style.position = 'absolute';
                        heart.style.fontSize = '2rem';
                        heart.style.color = '#ff6b6b';
                        heart.style.pointerEvents = 'none';
                        heart.style.animation = 'heartFloat 1s ease-out forwards';

                        const rect = this.getBoundingClientRect();
                        heart.style.left = rect.left + 'px';
                        heart.style.top = rect.top + 'px';

                        document.body.appendChild(heart);

                        setTimeout(() => {
                            document.body.removeChild(heart);
                        }, 1000);

                        // 更新点赞数
                        const countElement = this.querySelector('.action-count');
                        if (countElement) {
                            let count = parseInt(countElement.textContent.replace('k', '000').replace('.', ''));
                            count += 1;
                            if (count >= 1000) {
                                countElement.textContent = (count / 1000).toFixed(1) + 'k';
                            } else {
                                countElement.textContent = count;
                            }
                        }
                    }
                });
            });

            // 小红书风格的瀑布流加载动画
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            });

            const xhsCards = document.querySelectorAll('.project-card-xhs');
            xhsCards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
                observer.observe(card);
            });
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes heartFloat {
                0% {
                    transform: translateY(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateY(-50px) scale(1.5);
                    opacity: 0;
                }
            }

            .action-btn:active {
                transform: scale(0.95);
            }

            .project-card-xhs:hover .project-image {
                transform: scale(1.02);
                transition: transform 0.3s ease;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
