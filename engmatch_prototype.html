<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EngMatch - 工程项目撮合平台原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: bold;
        }
        
        .nav-menu {
            display: flex;
            gap: 2rem;
        }
        
        .nav-item {
            cursor: pointer;
            padding: 0.5rem 1rem;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .nav-item:hover, .nav-item.active {
            background: rgba(255,255,255,0.2);
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        /* 页面内容区域 */
        .page {
            display: none;
            padding: 2rem;
            min-height: calc(100vh - 80px);
        }
        
        .page.active {
            display: block;
        }
        
        /* 首页样式 */
        .hero-section {
            text-align: center;
            padding: 4rem 0;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            margin: -2rem -2rem 2rem -2rem;
        }
        
        .hero-title {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .hero-subtitle {
            font-size: 1.2rem;
            margin-bottom: 2rem;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }
        
        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 1rem;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #4CAF50;
            color: white;
        }
        
        .btn-secondary {
            background: white;
            color: #333;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        /* 功能卡片 */
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        /* 项目列表 */
        .project-filters {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }
        
        .filter-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #ddd;
            background: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .filter-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .project-list {
            display: grid;
            gap: 1.5rem;
        }
        
        .project-card {
            background: white;
            border-radius: 10px;
            padding: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .project-header {
            display: flex;
            justify-content: space-between;
            align-items: start;
            margin-bottom: 1rem;
        }
        
        .project-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #333;
        }
        
        .project-status {
            padding: 0.3rem 0.8rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-open {
            background: #e8f5e8;
            color: #4CAF50;
        }
        
        .status-bidding {
            background: #fff3cd;
            color: #856404;
        }
        
        .project-meta {
            display: flex;
            gap: 2rem;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #666;
        }
        
        .project-description {
            color: #666;
            line-height: 1.6;
            margin-bottom: 1rem;
        }
        
        .project-actions {
            display: flex;
            gap: 1rem;
        }
        
        /* 表单样式 */
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
        }
        
        .form-input {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
        }
        
        .form-textarea {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            min-height: 120px;
            resize: vertical;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .navbar {
                flex-direction: column;
                gap: 1rem;
            }
            
            .nav-menu {
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .hero-title {
                font-size: 2rem;
            }
            
            .cta-buttons {
                flex-direction: column;
                align-items: center;
            }
            
            .project-header {
                flex-direction: column;
                gap: 1rem;
            }
            
            .project-meta {
                flex-direction: column;
                gap: 0.5rem;
            }
        }
        
        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        
        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 2rem;
            border-radius: 10px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
        }

        /* 个人中心样式 */
        .profile-container {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 2rem;
        }

        .profile-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }

        .avatar {
            font-size: 4rem;
            margin-bottom: 1rem;
        }

        .profile-stats {
            display: flex;
            justify-content: space-around;
            margin-top: 2rem;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            display: block;
            font-size: 1.5rem;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #666;
        }

        .profile-tabs {
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 2rem;
        }

        .tab-btn {
            padding: 1rem 2rem;
            border: none;
            background: none;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .info-item {
            display: flex;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 5px;
        }

        .info-item label {
            font-weight: bold;
            margin-right: 1rem;
            min-width: 80px;
        }

        .project-summary {
            display: flex;
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .summary-item {
            text-align: center;
            padding: 1.5rem;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            flex: 1;
        }

        .summary-number {
            display: block;
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        .summary-label {
            color: #666;
            margin-top: 0.5rem;
        }

        .certificate-list {
            display: grid;
            gap: 1rem;
        }

        .certificate-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            background: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .cert-status {
            color: #4CAF50;
            font-weight: bold;
        }

        /* 消息页面样式 */
        .messages-container {
            display: grid;
            grid-template-columns: 350px 1fr;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 10px;
            overflow: hidden;
        }

        .message-sidebar {
            background: #f8f9fa;
            border-right: 1px solid #ddd;
        }

        .message-search {
            padding: 1rem;
            border-bottom: 1px solid #ddd;
        }

        .contact-list {
            overflow-y: auto;
            height: calc(100% - 80px);
        }

        .contact-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            transition: background 0.3s;
        }

        .contact-item:hover, .contact-item.active {
            background: white;
        }

        .contact-avatar {
            font-size: 2rem;
            margin-right: 1rem;
        }

        .contact-info {
            flex: 1;
        }

        .contact-name {
            font-weight: bold;
            margin-bottom: 0.3rem;
        }

        .contact-message {
            font-size: 0.9rem;
            color: #666;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-time {
            font-size: 0.8rem;
            color: #999;
        }

        .chat-area {
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
            background: white;
        }

        .chat-actions {
            display: flex;
            gap: 0.5rem;
        }

        .chat-messages {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 1rem;
            display: flex;
            flex-direction: column;
        }

        .message.sent {
            align-items: flex-end;
        }

        .message.received {
            align-items: flex-start;
        }

        .message-content {
            max-width: 70%;
            padding: 0.8rem 1rem;
            border-radius: 10px;
            word-wrap: break-word;
        }

        .message.sent .message-content {
            background: #667eea;
            color: white;
        }

        .message.received .message-content {
            background: white;
            border: 1px solid #ddd;
        }

        .message .message-time {
            font-size: 0.8rem;
            color: #999;
            margin-top: 0.3rem;
        }

        .chat-input {
            display: flex;
            padding: 1rem;
            border-top: 1px solid #ddd;
            background: white;
            gap: 1rem;
        }

        .message-input {
            flex: 1;
            padding: 0.8rem;
            border: 1px solid #ddd;
            border-radius: 20px;
            outline: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 导航栏 -->
        <nav class="navbar">
            <div class="logo">EngMatch</div>
            <div class="nav-menu">
                <div class="nav-item active" onclick="showPage('home')">首页</div>
                <div class="nav-item" onclick="showPage('projects')">项目大厅</div>
                <div class="nav-item" onclick="showPage('publish')">发布项目</div>
                <div class="nav-item" onclick="showPage('profile')">个人中心</div>
                <div class="nav-item" onclick="showPage('messages')">消息</div>
            </div>
            <div class="user-info">
                <span>欢迎，张工程师</span>
                <button class="btn btn-secondary" onclick="showLoginModal()">登录</button>
            </div>
        </nav>

        <!-- 首页 -->
        <div id="home" class="page active">
            <div class="hero-section">
                <h1 class="hero-title">EngMatch</h1>
                <p class="hero-subtitle">全球工程项目撮合与居间服务平台</p>
                <div class="cta-buttons">
                    <button class="btn btn-primary" onclick="showPage('projects')">浏览项目</button>
                    <button class="btn btn-secondary" onclick="showPage('publish')">发布项目</button>
                </div>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🏗️</div>
                    <h3>项目撮合</h3>
                    <p>智能匹配算法，精准连接项目方与施工方，提高撮合效率</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <h3>安全托管</h3>
                    <p>专业的支付托管服务，保障交易安全，降低合作风险</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📊</div>
                    <h3>信用评估</h3>
                    <p>完善的信用评级体系，实时风险评估，建立行业信任</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚖️</div>
                    <h3>居间服务</h3>
                    <p>专业的纠纷调解和法律咨询，全程保障项目顺利进行</p>
                </div>
            </div>
        </div>

        <!-- 项目大厅 -->
        <div id="projects" class="page">
            <h2>项目大厅</h2>
            <div class="project-filters">
                <button class="filter-btn active" onclick="filterProjects('all')">全部项目</button>
                <button class="filter-btn" onclick="filterProjects('building')">建筑工程</button>
                <button class="filter-btn" onclick="filterProjects('road')">道路桥梁</button>
                <button class="filter-btn" onclick="filterProjects('water')">水利工程</button>
                <button class="filter-btn" onclick="filterProjects('electric')">电力工程</button>
                <button class="filter-btn" onclick="filterProjects('open')">招标中</button>
            </div>

            <div class="project-list">
                <div class="project-card" data-category="building">
                    <div class="project-header">
                        <h3 class="project-title">北京CBD商业综合体建设项目</h3>
                        <span class="project-status status-open">招标中</span>
                    </div>
                    <div class="project-meta">
                        <span>📍 北京市朝阳区</span>
                        <span>💰 预算：5000万元</span>
                        <span>⏰ 工期：24个月</span>
                        <span>👥 已投标：12家</span>
                    </div>
                    <p class="project-description">
                        建设面积约8万平方米的商业综合体，包含购物中心、办公楼、酒店等业态。
                        要求具备一级建筑资质，有类似大型商业项目经验。
                    </p>
                    <div class="project-actions">
                        <button class="btn btn-primary" onclick="showBidModal('project1')">立即投标</button>
                        <button class="btn btn-secondary" onclick="showProjectDetail('project1')">查看详情</button>
                    </div>
                </div>

                <div class="project-card" data-category="road">
                    <div class="project-header">
                        <h3 class="project-title">杭州湾跨海大桥维护工程</h3>
                        <span class="project-status status-bidding">评标中</span>
                    </div>
                    <div class="project-meta">
                        <span>📍 浙江省嘉兴市</span>
                        <span>💰 预算：1200万元</span>
                        <span>⏰ 工期：6个月</span>
                        <span>👥 已投标：8家</span>
                    </div>
                    <p class="project-description">
                        对杭州湾跨海大桥进行定期维护保养，包括桥面修复、防腐处理、监测设备更新等。
                        要求具备桥梁专业承包资质。
                    </p>
                    <div class="project-actions">
                        <button class="btn btn-primary" disabled>投标已截止</button>
                        <button class="btn btn-secondary" onclick="showProjectDetail('project2')">查看详情</button>
                    </div>
                </div>

                <div class="project-card" data-category="water">
                    <div class="project-header">
                        <h3 class="project-title">成都市污水处理厂升级改造</h3>
                        <span class="project-status status-open">招标中</span>
                    </div>
                    <div class="project-meta">
                        <span>📍 四川省成都市</span>
                        <span>💰 预算：3000万元</span>
                        <span>⏰ 工期：18个月</span>
                        <span>👥 已投标：5家</span>
                    </div>
                    <p class="project-description">
                        对现有污水处理设施进行升级改造，提升处理能力和出水标准。
                        要求具备环保工程专业承包资质。
                    </p>
                    <div class="project-actions">
                        <button class="btn btn-primary" onclick="showBidModal('project3')">立即投标</button>
                        <button class="btn btn-secondary" onclick="showProjectDetail('project3')">查看详情</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 发布项目页面 -->
        <div id="publish" class="page">
            <h2>发布项目</h2>
            <form class="project-form" onsubmit="submitProject(event)">
                <div class="form-group">
                    <label class="form-label">项目名称</label>
                    <input type="text" class="form-input" placeholder="请输入项目名称" required>
                </div>

                <div class="form-group">
                    <label class="form-label">项目类型</label>
                    <select class="form-input" required>
                        <option value="">请选择项目类型</option>
                        <option value="building">建筑工程</option>
                        <option value="road">道路桥梁</option>
                        <option value="water">水利工程</option>
                        <option value="electric">电力工程</option>
                        <option value="other">其他工程</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">项目地址</label>
                    <input type="text" class="form-input" placeholder="请输入项目所在地址" required>
                </div>

                <div class="form-group">
                    <label class="form-label">项目预算（万元）</label>
                    <input type="number" class="form-input" placeholder="请输入项目预算" required>
                </div>

                <div class="form-group">
                    <label class="form-label">计划工期（月）</label>
                    <input type="number" class="form-input" placeholder="请输入计划工期" required>
                </div>

                <div class="form-group">
                    <label class="form-label">资质要求</label>
                    <select class="form-input" required>
                        <option value="">请选择资质要求</option>
                        <option value="level1">一级资质</option>
                        <option value="level2">二级资质</option>
                        <option value="level3">三级资质</option>
                        <option value="special">专业承包资质</option>
                    </select>
                </div>

                <div class="form-group">
                    <label class="form-label">项目描述</label>
                    <textarea class="form-textarea" placeholder="请详细描述项目需求、技术要求、注意事项等" required></textarea>
                </div>

                <div class="form-group">
                    <label class="form-label">投标截止时间</label>
                    <input type="datetime-local" class="form-input" required>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%;">发布项目</button>
                </div>
            </form>
        </div>

        <!-- 个人中心页面 -->
        <div id="profile" class="page">
            <h2>个人中心</h2>
            <div class="profile-container">
                <div class="profile-sidebar">
                    <div class="profile-card">
                        <div class="avatar">👨‍💼</div>
                        <h3>张工程师</h3>
                        <p>高级建筑工程师</p>
                        <div class="profile-stats">
                            <div class="stat-item">
                                <span class="stat-number">15</span>
                                <span class="stat-label">完成项目</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">4.8</span>
                                <span class="stat-label">信用评分</span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number">98%</span>
                                <span class="stat-label">履约率</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="profile-content">
                    <div class="profile-tabs">
                        <button class="tab-btn active" onclick="showProfileTab('info')">基本信息</button>
                        <button class="tab-btn" onclick="showProfileTab('projects')">我的项目</button>
                        <button class="tab-btn" onclick="showProfileTab('bids')">投标记录</button>
                        <button class="tab-btn" onclick="showProfileTab('certificates')">资质证书</button>
                    </div>

                    <div id="profile-info" class="tab-content active">
                        <h3>基本信息</h3>
                        <div class="info-grid">
                            <div class="info-item">
                                <label>姓名：</label>
                                <span>张工程师</span>
                            </div>
                            <div class="info-item">
                                <label>手机：</label>
                                <span>138****8888</span>
                            </div>
                            <div class="info-item">
                                <label>邮箱：</label>
                                <span><EMAIL></span>
                            </div>
                            <div class="info-item">
                                <label>公司：</label>
                                <span>北京建设工程有限公司</span>
                            </div>
                            <div class="info-item">
                                <label>职位：</label>
                                <span>高级建筑工程师</span>
                            </div>
                            <div class="info-item">
                                <label>从业年限：</label>
                                <span>12年</span>
                            </div>
                        </div>
                    </div>

                    <div id="profile-projects" class="tab-content">
                        <h3>我的项目</h3>
                        <div class="project-summary">
                            <div class="summary-item">
                                <span class="summary-number">3</span>
                                <span class="summary-label">进行中</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-number">15</span>
                                <span class="summary-label">已完成</span>
                            </div>
                            <div class="summary-item">
                                <span class="summary-number">2</span>
                                <span class="summary-label">待开始</span>
                            </div>
                        </div>
                    </div>

                    <div id="profile-bids" class="tab-content">
                        <h3>投标记录</h3>
                        <p>最近投标的项目列表...</p>
                    </div>

                    <div id="profile-certificates" class="tab-content">
                        <h3>资质证书</h3>
                        <div class="certificate-list">
                            <div class="certificate-item">
                                <span>✅ 一级建造师证书</span>
                                <span class="cert-status">已认证</span>
                            </div>
                            <div class="certificate-item">
                                <span>✅ 建筑工程施工总承包一级资质</span>
                                <span class="cert-status">已认证</span>
                            </div>
                            <div class="certificate-item">
                                <span>✅ 安全生产许可证</span>
                                <span class="cert-status">已认证</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 消息页面 -->
        <div id="messages" class="page">
            <h2>消息中心</h2>
            <div class="messages-container">
                <div class="message-sidebar">
                    <div class="message-search">
                        <input type="text" placeholder="搜索联系人..." class="form-input">
                    </div>
                    <div class="contact-list">
                        <div class="contact-item active" onclick="showChat('contact1')">
                            <div class="contact-avatar">🏢</div>
                            <div class="contact-info">
                                <div class="contact-name">上海建设集团</div>
                                <div class="contact-message">关于CBD项目的投标事宜...</div>
                            </div>
                            <div class="message-time">10:30</div>
                        </div>
                        <div class="contact-item" onclick="showChat('contact2')">
                            <div class="contact-avatar">👨‍💼</div>
                            <div class="contact-info">
                                <div class="contact-name">李项目经理</div>
                                <div class="contact-message">项目进度汇报</div>
                            </div>
                            <div class="message-time">昨天</div>
                        </div>
                        <div class="contact-item" onclick="showChat('contact3')">
                            <div class="contact-avatar">🏗️</div>
                            <div class="contact-info">
                                <div class="contact-name">系统通知</div>
                                <div class="contact-message">您有新的投标邀请</div>
                            </div>
                            <div class="message-time">2天前</div>
                        </div>
                    </div>
                </div>

                <div class="chat-area">
                    <div class="chat-header">
                        <h3>上海建设集团</h3>
                        <div class="chat-actions">
                            <button class="btn btn-secondary">📞 语音通话</button>
                            <button class="btn btn-secondary">📹 视频通话</button>
                        </div>
                    </div>

                    <div class="chat-messages">
                        <div class="message received">
                            <div class="message-content">
                                您好，我们对您发布的CBD商业综合体项目很感兴趣，希望能详细了解一下项目需求。
                            </div>
                            <div class="message-time">10:25</div>
                        </div>
                        <div class="message sent">
                            <div class="message-content">
                                您好！感谢您的关注。项目详细需求已经在项目描述中说明，如有具体问题可以随时沟通。
                            </div>
                            <div class="message-time">10:28</div>
                        </div>
                        <div class="message received">
                            <div class="message-content">
                                好的，我们会仔细研究项目需求，预计明天提交投标方案。
                            </div>
                            <div class="message-time">10:30</div>
                        </div>
                    </div>

                    <div class="chat-input">
                        <input type="text" placeholder="输入消息..." class="message-input">
                        <button class="btn btn-primary">发送</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 登录模态框 -->
        <div id="loginModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>用户登录</h3>
                    <button class="close-btn" onclick="closeModal('loginModal')">&times;</button>
                </div>
                <form onsubmit="login(event)">
                    <div class="form-group">
                        <label class="form-label">手机号/邮箱</label>
                        <input type="text" class="form-input" placeholder="请输入手机号或邮箱" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">密码</label>
                        <input type="password" class="form-input" placeholder="请输入密码" required>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">登录</button>
                    </div>
                    <div class="form-group" style="text-align: center;">
                        <a href="#" onclick="showRegisterModal()">还没有账号？立即注册</a>
                    </div>
                </form>
            </div>
        </div>

        <!-- 投标模态框 -->
        <div id="bidModal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h3>项目投标</h3>
                    <button class="close-btn" onclick="closeModal('bidModal')">&times;</button>
                </div>
                <form onsubmit="submitBid(event)">
                    <div class="form-group">
                        <label class="form-label">投标价格（万元）</label>
                        <input type="number" class="form-input" placeholder="请输入投标价格" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">工期承诺（月）</label>
                        <input type="number" class="form-input" placeholder="请输入工期承诺" required>
                    </div>
                    <div class="form-group">
                        <label class="form-label">技术方案</label>
                        <textarea class="form-textarea" placeholder="请简述您的技术方案和优势" required></textarea>
                    </div>
                    <div class="form-group">
                        <label class="form-label">上传资质文件</label>
                        <input type="file" class="form-input" multiple accept=".pdf,.jpg,.png">
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" style="width: 100%;">提交投标</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        // 页面切换功能
        function showPage(pageId) {
            // 隐藏所有页面
            const pages = document.querySelectorAll('.page');
            pages.forEach(page => page.classList.remove('active'));

            // 显示目标页面
            document.getElementById(pageId).classList.add('active');

            // 更新导航状态
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 项目筛选功能
        function filterProjects(category) {
            const projects = document.querySelectorAll('.project-card');
            const filterBtns = document.querySelectorAll('.filter-btn');

            // 更新按钮状态
            filterBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 筛选项目
            projects.forEach(project => {
                if (category === 'all') {
                    project.style.display = 'block';
                } else if (category === 'open') {
                    const status = project.querySelector('.project-status');
                    project.style.display = status.textContent.includes('招标中') ? 'block' : 'none';
                } else {
                    const projectCategory = project.getAttribute('data-category');
                    project.style.display = projectCategory === category ? 'block' : 'none';
                }
            });
        }

        // 个人中心标签切换
        function showProfileTab(tabId) {
            // 隐藏所有标签内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 显示目标标签内容
            document.getElementById('profile-' + tabId).classList.add('active');

            // 更新标签按钮状态
            const tabBtns = document.querySelectorAll('.tab-btn');
            tabBtns.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');
        }

        // 显示聊天
        function showChat(contactId) {
            const contacts = document.querySelectorAll('.contact-item');
            contacts.forEach(contact => contact.classList.remove('active'));
            event.target.classList.add('active');

            // 这里可以加载对应联系人的聊天记录
            console.log('切换到联系人:', contactId);
        }

        // 模态框功能
        function showLoginModal() {
            document.getElementById('loginModal').style.display = 'block';
        }

        function showBidModal(projectId) {
            document.getElementById('bidModal').style.display = 'block';
            console.log('投标项目:', projectId);
        }

        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 表单提交功能
        function submitProject(event) {
            event.preventDefault();
            alert('项目发布成功！我们将在24小时内审核您的项目。');
            showPage('projects');
        }

        function submitBid(event) {
            event.preventDefault();
            alert('投标提交成功！请等待项目方审核。');
            closeModal('bidModal');
        }

        function login(event) {
            event.preventDefault();
            alert('登录成功！');
            closeModal('loginModal');
            // 更新用户信息显示
            document.querySelector('.user-info span').textContent = '欢迎，张工程师';
        }

        function showProjectDetail(projectId) {
            alert('查看项目详情: ' + projectId);
            // 这里可以跳转到项目详情页面
        }

        function showRegisterModal() {
            closeModal('loginModal');
            alert('注册功能开发中...');
        }

        // 点击模态框外部关闭
        window.onclick = function(event) {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => {
                if (event.target === modal) {
                    modal.style.display = 'none';
                }
            });
        }

        // 消息发送功能
        document.addEventListener('DOMContentLoaded', function() {
            const messageInput = document.querySelector('.message-input');
            const sendBtn = document.querySelector('.chat-input .btn-primary');

            function sendMessage() {
                const message = messageInput.value.trim();
                if (message) {
                    const chatMessages = document.querySelector('.chat-messages');
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'message sent';
                    messageDiv.innerHTML = `
                        <div class="message-content">${message}</div>
                        <div class="message-time">${new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'})}</div>
                    `;
                    chatMessages.appendChild(messageDiv);
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    messageInput.value = '';
                }
            }

            if (sendBtn) {
                sendBtn.addEventListener('click', sendMessage);
            }
            if (messageInput) {
                messageInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        sendMessage();
                    }
                });
            }
        });

        // 模拟实时数据更新
        setInterval(function() {
            // 模拟新消息通知
            const messageTime = document.querySelector('.contact-item.active .message-time');
            if (messageTime && Math.random() > 0.95) {
                messageTime.textContent = new Date().toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            }
        }, 5000);

        // 添加一些动画效果
        document.addEventListener('DOMContentLoaded', function() {
            // 为卡片添加悬停效果
            const cards = document.querySelectorAll('.feature-card, .project-card');
            cards.forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                });
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        });
    </script>
</body>
</html>
