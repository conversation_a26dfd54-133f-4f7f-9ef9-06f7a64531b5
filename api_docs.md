# EngMatch API文档

## 1. 项目管理接口

### 1.1 创建项目
POST /api/v1/projects
```json
{
  "name": "示例工程",
  "budget": 1000000,
  "description": "项目描述",
  "requirements": ["资质要求"]
}
```

### 1.2 项目列表
GET /api/v1/projects
```json
{
  "page": 1,
  "size": 10,
  "filters": {
    "status": "OPEN",
    "budget_range": [100000, 1000000]
  }
}
```

## 2. 投标接口

### 2.1 提交投标
POST /api/v1/projects/{projectId}/bids
```json
{
  "amount": 950000,
  "timeframe": 90,
  "proposal": "投标方案"
}
```

## 3. 交易接口

### 3.1 创建支付托管
POST /api/v1/transactions
```json
{
  "project_id": "123",
  "bid_id": "456",
  "amount": 950000,
  "payment_terms": ["30-30-40"]
}
```
