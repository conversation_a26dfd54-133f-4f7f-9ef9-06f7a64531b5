# EngMatch数据库设计

## 1. ER图
```mermaid
erDiagram
    PROJECT ||--o{ BID : receives
    PROJECT {
        int id
        string name
        decimal budget
        string status
    }
    COMPANY ||--o{ PROJECT : owns
    COMPANY {
        int id
        string name
        string license_no
        int credit_score
    }
    BID {
        int id
        int project_id
        int company_id
        decimal amount
    }
    TRANSACTION ||--|| BID : generates
    TRANSACTION {
        int id
        string status
        decimal amount
        timestamp created_at
    }
```

## 2. 核心表结构

### 2.1 项目表(t_project)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| name | varchar | 项目名称 |
| budget | decimal | 预算金额 |
| status | varchar | 项目状态 |

### 2.2 公司表(t_company)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| name | varchar | 公司名称 |
| credit_score | int | 信用分数 |
