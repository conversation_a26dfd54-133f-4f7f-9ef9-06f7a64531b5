# EngMatch系统流程图

## 1. 项目撮合流程
```mermaid
graph TD
    A[项目方] -->|发布项目| B[项目审核]
    B -->|通过| C[智能匹配]
    C -->|推送| D[施工方]
    D -->|投标| E[评估选标]
    E -->|中标| F[签订合同]
    F -->|支付托管| G[项目启动]
```

## 2. 支付流程
```mermaid
graph TD
    A[项目方] -->|托管付款| B[支付系统]
    B -->|验证| C[风控审核]
    C -->|通过| D[资金托管]
    D -->|节点完成| E[放款]
    E -->|确认| F[施工方]
```

## 3. 风控流程
```mermaid
graph TD
    A[交易发起] -->|实时检测| B[风控规则]
    B -->|通过| C[信用评估]
    C -->|通过| D[限额检查]
    D -->|通过| E[批准交易]
    B -->|拒绝| F[交易终止]
    C -->|拒绝| F
    D -->|拒绝| F
```
