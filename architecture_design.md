# EngMatch系统架构设计

## 1. 总体架构

### 1.1 技术栈选型
- 前端：React + TypeScript
- 后端：Spring Cloud
- 数据库：MySQL + MongoDB
- 缓存：Redis
- 消息队列：RabbitMQ
- 搜索引擎：Elasticsearch

### 1.2 系统架构图
```
[用户层]
Web/Mobile/H5
   ↓
[接入层]
Nginx/Gateway
   ↓
[应用层]
用户服务 - 项目服务 - 交易服务 - 风控服务
   ↓
[数据层]
MySQL/MongoDB/Redis/ES
```

## 2. 微服务架构

### 2.1 核心服务
- user-service: 用户中心
- project-service: 项目管理
- match-service: 撮合系统
- payment-service: 支付系统
- risk-service: 风控中心

### 2.2 中间件服务
- auth-service: 认证中心
- message-service: 消息系统
- file-service: 文件服务
- search-service: 搜索服务
